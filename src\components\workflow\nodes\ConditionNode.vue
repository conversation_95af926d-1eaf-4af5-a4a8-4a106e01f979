<template>
  <div class="condition-node">
    <!-- 主要连接点 -->
    <Handle
      type="target"
      :position="Position.Left"
      class="main-handle input-handle"
      :style="{
        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
        width: '16px',
        height: '16px',
        border: '3px solid white',
        borderRadius: '50%',
        boxShadow: '0 4px 12px rgba(245, 158, 11, 0.4), 0 0 0 2px rgba(245, 158, 11, 0.2)',
        transition: 'all 0.2s ease',
        cursor: 'crosshair',
      }"
    />

    <!-- 输出端口 -->
    <Handle
      id="true"
      type="source"
      :position="Position.Right"
      class="branch-handle true-handle"
      :style="{
        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
        width: '14px',
        height: '14px',
        border: '2px solid white',
        borderRadius: '50%',
        boxShadow: '0 3px 8px rgba(16, 185, 129, 0.4), 0 0 0 1px rgba(16, 185, 129, 0.2)',
        top: '35%',
        transition: 'all 0.2s ease',
        cursor: 'crosshair',
      }"
    />

    <Handle
      id="false"
      type="source"
      :position="Position.Right"
      class="branch-handle false-handle"
      :style="{
        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
        width: '14px',
        height: '14px',
        border: '2px solid white',
        borderRadius: '50%',
        boxShadow: '0 3px 8px rgba(239, 68, 68, 0.4), 0 0 0 1px rgba(239, 68, 68, 0.2)',
        top: '65%',
        transition: 'all 0.2s ease',
        cursor: 'crosshair',
      }"
    />

    <!-- 节点头部 -->
    <div class="node-header">
      <div class="node-icon-wrapper">
        <div class="node-icon">
          <div :class="data.icon || 'i-mdi:source-branch'" class="text-lg text-white"></div>
        </div>
      </div>
      <div class="node-info">
        <div class="node-title">{{ data.label }}</div>
        <div class="node-subtitle">条件节点</div>
      </div>
      <div class="node-status" :class="statusClass">
        <div :class="statusIcon"></div>
      </div>
    </div>

    <!-- 节点内容 -->
    <div class="node-content">
      <div class="node-description" v-if="data.metadata?.description">
        {{ data.metadata.description }}
      </div>

      <!-- 输入端口 -->
      <div class="inputs" v-if="data.inputs.length > 0">
        <div class="ports-title">输入</div>
        <div v-for="input in data.inputs" :key="input.id" class="input-port">
          <Handle
            :id="input.id"
            type="target"
            :position="Position.Left"
            :style="{
              background: getTypeColor(input.type),
              width: '10px',
              height: '10px',
              border: '2px solid white',
              boxShadow: `0 2px 6px ${getTypeColor(input.type)}40`,
            }"
          />
          <div class="port-info">
            <div class="port-label">
              {{ input.name }}
              <span v-if="input.required" class="required-mark">*</span>
            </div>
            <div
              class="port-type"
              :style="{ backgroundColor: getTypeColor(input.type) + '20', color: getTypeColor(input.type) }"
            >
              {{ input.type }}
            </div>
          </div>
        </div>
      </div>

      <!-- 条件配置显示 -->
      <div class="condition-display">
        <div class="condition-text">
          {{ getConditionText() }}
        </div>
      </div>

      <!-- 输出分支 -->
      <div class="outputs">
        <div class="ports-title">输出分支</div>
        <div class="output-branches">
          <div class="output-branch true-branch">
            <div class="branch-info">
              <div class="branch-label">True</div>
              <div class="branch-description">条件为真时的路径</div>
            </div>
            <div class="branch-indicator true-indicator"></div>
          </div>

          <div class="output-branch false-branch">
            <div class="branch-info">
              <div class="branch-label">False</div>
              <div class="branch-description">条件为假时的路径</div>
            </div>
            <div class="branch-indicator false-indicator"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 装饰性元素 -->
    <div class="node-decoration"></div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { Handle, Position } from '@vue-flow/core';
  import type { NodeData } from '@/types/workflow';

  const props = defineProps<{
    data: NodeData;
  }>();

  // 计算状态样式
  const statusClass = computed(() => {
    return 'status-ready';
  });

  const statusIcon = computed(() => {
    return 'i-mdi:check-circle';
  });

  // 根据数据类型返回颜色
  const getTypeColor = (type: string) => {
    const colors = {
      string: '#10b981',
      number: '#3b82f6',
      boolean: '#f59e0b',
      array: '#8b5cf6',
      object: '#ef4444',
      file: '#6b7280',
      image: '#ec4899',
      any: '#64748b',
    };
    return colors[type as keyof typeof colors] || '#64748b';
  };

  // 获取条件文本描述
  const getConditionText = () => {
    const config = props.data.config;
    const operator = config.operator || 'equals';
    const value = config.value || '';

    const operatorMap = {
      equals: '等于',
      notEquals: '不等于',
      greaterThan: '大于',
      lessThan: '小于',
      contains: '包含',
      startsWith: '开始于',
      endsWith: '结束于',
    };

    return `${operatorMap[operator as keyof typeof operatorMap] || operator} "${value}"`;
  };
</script>

<style scoped>
  .condition-node {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    min-width: 300px;
    max-width: 380px;
    border-radius: 0.75rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    background-color: white;
  }

  .condition-node:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 40px rgba(245, 158, 11, 0.3);
  }

  .node-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    position: relative;
    z-index: 10;
  }

  .node-icon-wrapper {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
  }

  .node-icon {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .node-info {
    flex: 1 1 0%;
  }

  .node-title {
    font-weight: 600;
    color: white;
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .node-subtitle {
    font-size: 0.75rem;
    line-height: 1rem;
    color: white;
    opacity: 0.8;
    margin-top: 0.25rem;
  }

  .node-status {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .status-ready {
    color: #6ee7b7;
  }

  .status-running {
    color: #fde68a;
  }

  .status-error {
    color: #fca5a5;
  }

  .node-content {
    padding-left: 1rem;
    padding-right: 1rem;
    padding-bottom: 1rem;
    position: relative;
    z-index: 10;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    margin: 0 1px 1px 1px;
    border-radius: 0 0 11px 11px;
  }

  .node-description {
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: #64748b;
    margin-bottom: 0.75rem;
    line-height: 1.625;
  }

  .inputs {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1rem;
  }

  .ports-title {
    font-size: 0.75rem;
    line-height: 1rem;
    font-weight: 600;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    margin-bottom: 0.5rem;
  }

  .input-port {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    border-radius: 0.5rem;
    background: rgba(245, 158, 11, 0.05);
    border: 1px solid rgba(245, 158, 11, 0.1);
    transition: all 0.2s ease;
  }

  .input-port:hover {
    background: rgba(245, 158, 11, 0.1);
    transform: translateX(-2px);
  }

  .port-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .port-label {
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 500;
    color: #374151;
  }

  .port-type {
    font-size: 0.75rem;
    line-height: 1rem;
    font-weight: 500;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
    border-radius: 9999px;
    font-family: 'JetBrains Mono', 'Consolas', monospace;
  }

  .required-mark {
    color: #ef4444;
    font-weight: 700;
    margin-left: 0.25rem;
  }

  .condition-display {
    background: linear-gradient(to right, #fffbeb, #ffedd5);
    padding: 0.75rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    border: 1px solid #fde68a;
  }

  .condition-text {
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: #92400e;
    font-family: 'JetBrains Mono', 'Consolas', monospace;
    font-weight: 500;
  }

  .outputs {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .output-branches {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .output-branch {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
  }

  .true-branch {
    background: rgba(16, 185, 129, 0.05);
    border: 1px solid rgba(16, 185, 129, 0.2);
  }

  .true-branch:hover {
    background: rgba(16, 185, 129, 0.1);
    transform: translateX(2px);
  }

  .false-branch {
    background: rgba(239, 68, 68, 0.05);
    border: 1px solid rgba(239, 68, 68, 0.2);
  }

  .false-branch:hover {
    background: rgba(239, 68, 68, 0.1);
    transform: translateX(2px);
  }

  .branch-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .branch-label {
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 600;
  }

  .true-branch .branch-label {
    color: #15803d;
  }

  .false-branch .branch-label {
    color: #b91c1c;
  }

  .branch-description {
    font-size: 0.75rem;
    line-height: 1rem;
    color: #64748b;
  }

  .branch-indicator {
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 9999px;
  }

  .true-indicator {
    background: linear-gradient(135deg, #10b981, #059669);
  }

  .false-indicator {
    background: linear-gradient(135deg, #ef4444, #dc2626);
  }

  .node-decoration {
    position: absolute;
    top: 0;
    right: 0;
    width: 60px;
    height: 60px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(30px, -30px);
    pointer-events: none;
  }

  :deep(.vue-flow__handle) {
    transition: all 0.2s ease;
  }

  :deep(.vue-flow__handle:hover) {
    transform: scale(1.2);
  }

  :deep(.vue-flow__handle-left) {
    left: -6px;
  }

  :deep(.vue-flow__handle-right) {
    right: -6px;
  }
</style>
