<template>
  <div class="input-node">
    <!-- 主要连接点 -->
    <Handle
      type="source"
      :position="Position.Right"
      class="main-handle"
      :style="{
        background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
        width: '16px',
        height: '16px',
        border: '3px solid white',
        borderRadius: '50%',
        boxShadow: '0 4px 12px rgba(59, 130, 246, 0.4), 0 0 0 2px rgba(59, 130, 246, 0.2)',
        transition: 'all 0.2s ease',
        cursor: 'crosshair',
      }"
    />

    <!-- 节点头部 -->
    <div class="node-header">
      <div class="node-icon-wrapper">
        <div class="node-icon">
          <div :class="data.icon || 'i-mdi:import'" class="text-lg text-white"></div>
        </div>
      </div>
      <div class="node-info">
        <div class="node-title">{{ data.label }}</div>
        <div class="node-subtitle">输入节点</div>
      </div>
      <div class="node-status" :class="statusClass">
        <div :class="statusIcon"></div>
      </div>
    </div>

    <!-- 节点内容 -->
    <div class="node-content">
      <div class="node-description" v-if="data.metadata?.description">
        {{ data.metadata.description }}
      </div>

      <!-- 输出端口 -->
      <div class="outputs" v-if="data.outputs.length > 0">
        <div class="ports-title">输出</div>
        <div v-for="output in data.outputs" :key="output.id" class="output-port">
          <div class="port-info">
            <div class="port-label">{{ output.name }}</div>
            <div
              class="port-type"
              :style="{ backgroundColor: getTypeColor(output.type) + '20', color: getTypeColor(output.type) }"
            >
              {{ output.type }}
            </div>
          </div>
          <Handle
            :id="output.id"
            type="source"
            :position="Position.Right"
            :style="{
              background: getTypeColor(output.type),
              width: '10px',
              height: '10px',
              border: '2px solid white',
              boxShadow: `0 2px 6px ${getTypeColor(output.type)}40`,
            }"
          />
        </div>
      </div>
    </div>

    <!-- 装饰性元素 -->
    <div class="node-decoration"></div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { Handle, Position } from '@vue-flow/core';
  import type { NodeData } from '@/types/workflow';

  const props = defineProps<{
    data: NodeData;
  }>();

  // 计算状态样式
  const statusClass = computed(() => {
    // 这里可以根据节点的执行状态返回不同的样式
    return 'status-ready';
  });

  const statusIcon = computed(() => {
    return 'i-mdi:check-circle';
  });

  // 根据数据类型返回颜色
  const getTypeColor = (type: string) => {
    const colors = {
      string: '#10b981',
      number: '#3b82f6',
      boolean: '#f59e0b',
      array: '#8b5cf6',
      object: '#ef4444',
      file: '#6b7280',
      image: '#ec4899',
      any: '#64748b',
    };
    return colors[type as keyof typeof colors] || '#64748b';
  };
</script>

<style scoped>
  .input-node {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    min-width: 280px;
    max-width: 320px;
    border-radius: 0.75rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    background-color: white;
  }

  .input-node:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
  }

  .node-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    position: relative;
    z-index: 10;
  }

  .node-icon-wrapper {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
  }

  .node-icon {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .node-info {
    flex: 1 1 0%;
  }

  .node-title {
    font-weight: 600;
    color: white;
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .node-subtitle {
    font-size: 0.75rem;
    line-height: 1rem;
    color: white;
    opacity: 0.8;
    margin-top: 0.25rem;
  }

  .node-status {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .status-ready {
    color: #6ee7b7;
  }

  .status-running {
    color: #fde68a;
  }

  .status-error {
    color: #fca5a5;
  }

  .node-content {
    padding-left: 1rem;
    padding-right: 1rem;
    padding-bottom: 1rem;
    position: relative;
    z-index: 10;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    margin: 0 1px 1px 1px;
    border-radius: 0 0 11px 11px;
  }

  .node-description {
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: #64748b;
    margin-bottom: 0.75rem;
  }

  .outputs {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .ports-title {
    font-size: 0.75rem;
    line-height: 1rem;
    font-weight: 600;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    margin-bottom: 0.5rem;
  }

  .output-port {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    border-radius: 0.5rem;
    background: rgba(59, 130, 246, 0.05);
    border: 1px solid rgba(59, 130, 246, 0.1);
    transition: all 0.2s ease;
  }

  .output-port:hover {
    background: rgba(59, 130, 246, 0.1);
    transform: translateX(2px);
  }

  .port-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .port-label {
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 500;
    color: #374151;
  }

  .port-type {
    font-size: 0.75rem;
    line-height: 1rem;
    font-weight: 500;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
    border-radius: 9999px;
    font-family: 'JetBrains Mono', 'Consolas', monospace;
  }

  .node-decoration {
    position: absolute;
    top: 0;
    right: 0;
    width: 60px;
    height: 60px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(30px, -30px);
    pointer-events: none;
  }

  :deep(.vue-flow__handle) {
    transition: all 0.2s ease;
  }

  :deep(.vue-flow__handle:hover) {
    transform: scale(1.2);
  }

  :deep(.vue-flow__handle-right) {
    right: -6px;
  }
</style>
