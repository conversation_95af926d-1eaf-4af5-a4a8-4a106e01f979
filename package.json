{"name": "irap-web", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build && node ./scripts/zip-dist.js", "preview": "vite preview", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite", "clean:lib": "rimraf node_modules", "lint": "eslint . --ext .vue,.ts,.tsx,.cjs,.mjs --ignore-pattern dist --fix", "format": "prettier --write src/", "genswagger": "node ./scripts/generateApi.js http://fast9.shenzhuo.vip:32271/resources/v3/api-docs --part --swagger", "genmap": "node ./scripts/api_mapping_script.js", "genapi": "npm run genmap && npm run genswagger"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vue-flow/background": "^1.3.2", "@vue-flow/controls": "^1.1.3", "@vue-flow/core": "^1.46.3", "@vue-flow/minimap": "^1.5.4", "@vue-flow/node-resizer": "^1.5.0", "@vueuse/core": "^13.1.0", "axios": "^1.9.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^6.0.0", "element-china-area-data": "^6.1.0", "element-plus": "^2.9.8", "lodash-es": "^4.17.21", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.2.0", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/css": "^0.7.0", "@eslint/js": "^9.25.1", "@eslint/json": "^0.12.0", "@iconify/json": "^2.2.333", "@types/crypto-js": "^4.2.2", "@types/lodash-es": "^4.17.12", "@types/node": "^22.15.2", "@unocss/eslint-config": "^66.1.1", "@unocss/preset-icons": "^66.1.1", "@unocss/reset": "^66.1.1", "@vitejs/plugin-vue": "^5.2.2", "@vue/tsconfig": "^0.7.0", "archiver": "^7.0.1", "chinese-to-pinyin": "^1.3.1", "eslint": "^9.25.1", "eslint-plugin-vue": "^10.0.0", "globals": "^16.0.0", "prettier": "^3.5.3", "rimraf": "^6.0.1", "sass": "^1.87.0", "ts-node": "^10.9.2", "typescript": "~5.7.2", "typescript-eslint": "^8.31.0", "unocss": "^66.1.1", "unplugin-auto-import": "^19.1.2", "vite": "^6.3.1", "vite-plugin-vue-devtools": "^7.7.5", "vue-tsc": "^2.2.8"}}